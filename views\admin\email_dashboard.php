<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid mt-3">
    <!-- Dashboard Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="mb-0">
                    <i class="fas fa-envelope-open-text"></i> Email Dashboard
                </h2>
                <div class="d-flex gap-2">
                    <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#createFolderModal">
                        <i class="fas fa-folder-plus"></i> New Folder
                    </button>
                    <button class="btn btn-success btn-sm" onclick="refreshDashboard()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards (Desktop/Tablet Only) -->
    <div class="row mb-4 d-none d-md-flex">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= $data['stats']['unread'] ?></h4>
                            <p class="mb-0">Unread Messages</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-envelope fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= $data['stats']['today'] ?></h4>
                            <p class="mb-0">Today's Emails</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-day fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= $data['stats']['pending_reminders'] ?></h4>
                            <p class="mb-0">Pending Reminders</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-bell fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= $data['stats']['owned'] ?></h4>
                            <p class="mb-0">Owned Messages</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Sidebar with Folders (Desktop/Tablet Only) -->
        <div class="col-md-3 d-none d-md-block">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-folder"></i> Folders
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        <a href="<?= URLROOT ?>/admin/email_dashboard" 
                           class="list-group-item list-group-item-action <?= !$data['current_folder'] ? 'active' : '' ?>">
                            <i class="fas fa-inbox"></i> All Messages
                            <span class="badge bg-primary rounded-pill float-end"><?= $data['stats']['unread'] ?></span>
                        </a>
                        
                        <?php foreach ($data['folders'] as $folder): ?>
                        <a href="<?= URLROOT ?>/admin/email_dashboard?folder=<?= $folder->id ?>" 
                           class="list-group-item list-group-item-action <?= $data['current_folder'] == $folder->id ? 'active' : '' ?>">
                            <i class="fas fa-folder" style="color: <?= htmlspecialchars($folder->color) ?>"></i>
                            <?= htmlspecialchars($folder->name) ?>
                            <?php if (!$folder->is_system): ?>
                            <button class="btn btn-sm btn-outline-danger float-end" 
                                    onclick="deleteFolder(<?= $folder->id ?>)" 
                                    title="Delete Folder">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                            <?php endif; ?>
                        </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- Pending Reminders (Desktop/Tablet Only) -->
            <?php if (!empty($data['reminders'])): ?>
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-bell"></i> Pending Reminders
                    </h6>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        <?php foreach (array_slice($data['reminders'], 0, 5) as $reminder): ?>
                        <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1"><?= htmlspecialchars($reminder->subject) ?></h6>
                                <small><?= date('M j, g:i A', strtotime($reminder->reminder_time)) ?></small>
                            </div>
                            <p class="mb-1"><?= htmlspecialchars($reminder->note) ?></p>
                            <small>Ticket: <?= htmlspecialchars($reminder->ticket_number) ?></small>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <!-- Main Content Area -->
        <div class="col-md-9">
            <!-- Search and Controls -->
            <div class="card mb-3">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" class="form-control" id="searchInput" 
                                       placeholder="Search messages..." value="<?= htmlspecialchars($data['search_query']) ?>">
                                <button class="btn btn-outline-secondary" type="button" onclick="searchMessages()">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex gap-2">
                                <select class="form-select" id="sortBy" onchange="changeSorting()">
                                    <option value="created_at" <?= $data['sort_by'] === 'created_at' ? 'selected' : '' ?>>Date</option>
                                    <option value="subject" <?= $data['sort_by'] === 'subject' ? 'selected' : '' ?>>Subject</option>
                                    <option value="is_read" <?= $data['sort_by'] === 'is_read' ? 'selected' : '' ?>>Read Status</option>
                                    <option value="priority" <?= $data['sort_by'] === 'priority' ? 'selected' : '' ?>>Priority</option>
                                    <option value="ticket_number" <?= $data['sort_by'] === 'ticket_number' ? 'selected' : '' ?>>Ticket</option>
                                </select>
                                <button class="btn btn-outline-secondary" onclick="toggleSortOrder()">
                                    <i class="fas fa-sort-<?= $data['sort_order'] === 'ASC' ? 'up' : 'down' ?>"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Messages List -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Messages</h5>
                </div>
                <div class="card-body p-0">
                    <?php if (empty($data['messages'])): ?>
                    <div class="text-center p-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No messages found</h5>
                        <p class="text-muted">There are no messages in this folder.</p>
                    </div>
                    <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th width="5%">
                                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                    </th>
                                    <th width="15%">From</th>
                                    <th width="35%">Subject</th>
                                    <th width="10%">Ticket</th>
                                    <th width="10%">Priority</th>
                                    <th width="15%">Date</th>
                                    <th width="10%">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($data['messages'] as $message): ?>
                                <tr class="<?= !$message->is_read ? 'table-warning' : '' ?>" 
                                    data-message-id="<?= $message->id ?>">
                                    <td>
                                        <input type="checkbox" class="message-checkbox" value="<?= $message->id ?>">
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <?php if ($message->owned_by_admin_id): ?>
                                            <i class="fas fa-user-lock text-info me-1" title="Owned by <?= htmlspecialchars($message->owner_name) ?>"></i>
                                            <?php endif; ?>
                                            <span><?= htmlspecialchars($message->sender_name ?: $message->original_sender_email) ?></span>
                                        </div>
                                    </td>
                                    <td>
                                        <a href="<?= URLROOT ?>/admin/viewEmailMessage/<?= $message->id ?>"
                                           class="text-decoration-none">
                                            <?= htmlspecialchars($message->subject) ?>
                                        </a>
                                        <?php if ($message->folder_name): ?>
                                        <br><small class="text-muted">
                                            <i class="fas fa-folder" style="color: <?= $message->folder_color ?>"></i>
                                            <?= htmlspecialchars($message->folder_name) ?>
                                        </small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($message->ticket_number): ?>
                                        <span class="badge bg-secondary"><?= htmlspecialchars($message->ticket_number) ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                        $priorityColors = [
                                            'low' => 'success',
                                            'normal' => 'secondary',
                                            'high' => 'warning',
                                            'urgent' => 'danger'
                                        ];
                                        $priorityColor = $priorityColors[$message->priority] ?? 'secondary';
                                        ?>
                                        <span class="badge bg-<?= $priorityColor ?>"><?= ucfirst($message->priority) ?></span>
                                    </td>
                                    <td>
                                        <small><?= date('M j, Y g:i A', strtotime($message->created_at)) ?></small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" 
                                                    onclick="showMoveToFolderModal(<?= $message->id ?>)" 
                                                    title="Move to Folder">
                                                <i class="fas fa-folder-open"></i>
                                            </button>
                                            <button class="btn btn-outline-warning" 
                                                    onclick="showReminderModal(<?= $message->id ?>)" 
                                                    title="Set Reminder">
                                                <i class="fas fa-bell"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Bulk Actions (Desktop/Tablet Only) -->
            <div class="card mt-3 d-none d-md-block">
                <div class="card-body">
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary" onclick="bulkMoveToFolder()" disabled id="bulkMoveBtn">
                            <i class="fas fa-folder-open"></i> Move Selected
                        </button>
                        <button class="btn btn-outline-warning" onclick="bulkSetReminder()" disabled id="bulkReminderBtn">
                            <i class="fas fa-bell"></i> Set Reminder
                        </button>
                        <button class="btn btn-outline-success" onclick="bulkMarkAsRead()" disabled id="bulkReadBtn">
                            <i class="fas fa-envelope-open"></i> Mark as Read
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Folder Modal -->
<div class="modal fade" id="createFolderModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="<?= URLROOT ?>/admin/email_dashboard/createFolder" method="POST">
                <div class="modal-header">
                    <h5 class="modal-title">Create New Folder</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="folder_name" class="form-label">Folder Name</label>
                        <input type="text" class="form-control" id="folder_name" name="folder_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="folder_color" class="form-label">Folder Color</label>
                        <input type="color" class="form-control form-control-color" id="folder_color" name="folder_color" value="#007bff">
                    </div>
                    <input type="hidden" name="csrf_token" value="<?= $data['csrf_token'] ?>">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Folder</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Move to Folder Modal -->
<div class="modal fade" id="moveToFolderModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Move to Folder</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="target_folder" class="form-label">Select Folder</label>
                    <select class="form-select" id="target_folder">
                        <option value="0">Inbox (No Folder)</option>
                        <?php foreach ($data['folders'] as $folder): ?>
                        <option value="<?= $folder->id ?>"><?= htmlspecialchars($folder->name) ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="confirmMoveToFolder()">Move</button>
            </div>
        </div>
    </div>
</div>

<!-- Set Reminder Modal -->
<div class="modal fade" id="reminderModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Set Reminder</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="reminder_time" class="form-label">Reminder Time</label>
                    <input type="datetime-local" class="form-control" id="reminder_time" required>
                </div>
                <div class="mb-3">
                    <label for="reminder_note" class="form-label">Note (Optional)</label>
                    <textarea class="form-control" id="reminder_note" rows="3" placeholder="Add a note for this reminder..."></textarea>
                </div>
                <div class="mb-3">
                    <label class="form-label">Quick Options</label>
                    <div class="d-flex gap-2 flex-wrap">
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setQuickReminder(15)">15 min</button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setQuickReminder(60)">1 hour</button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setQuickReminder(1440)">1 day</button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setQuickReminder(10080)">1 week</button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setQuickReminder(43200)">1 month</button>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" onclick="confirmSetReminder()">Set Reminder</button>
            </div>
        </div>
    </div>
</div>

<script>
let currentMessageId = null;
let selectedMessages = [];

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    updateBulkActionButtons();

    // Auto-refresh every 5 minutes
    setInterval(function() {
        if (document.visibilityState === 'visible') {
            refreshDashboard();
        }
    }, 300000);
});

// Search functionality
function searchMessages() {
    const query = document.getElementById('searchInput').value;
    const url = new URL(window.location);
    if (query) {
        url.searchParams.set('search', query);
    } else {
        url.searchParams.delete('search');
    }
    window.location.href = url.toString();
}

// Enter key search
document.getElementById('searchInput').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        searchMessages();
    }
});

// Sorting functionality
function changeSorting() {
    const sortBy = document.getElementById('sortBy').value;
    const url = new URL(window.location);
    url.searchParams.set('sort', sortBy);
    window.location.href = url.toString();
}

function toggleSortOrder() {
    const url = new URL(window.location);
    const currentOrder = url.searchParams.get('order') || 'DESC';
    const newOrder = currentOrder === 'ASC' ? 'DESC' : 'ASC';
    url.searchParams.set('order', newOrder);
    window.location.href = url.toString();
}

// Refresh dashboard
function refreshDashboard() {
    window.location.reload();
}

// Checkbox functionality
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.message-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });

    updateSelectedMessages();
}

function updateSelectedMessages() {
    const checkboxes = document.querySelectorAll('.message-checkbox:checked');
    selectedMessages = Array.from(checkboxes).map(cb => parseInt(cb.value));
    updateBulkActionButtons();
}

function updateBulkActionButtons() {
    const hasSelection = selectedMessages.length > 0;
    document.getElementById('bulkMoveBtn').disabled = !hasSelection;
    document.getElementById('bulkReminderBtn').disabled = !hasSelection;
    document.getElementById('bulkReadBtn').disabled = !hasSelection;
}

// Add event listeners to checkboxes
document.addEventListener('change', function(e) {
    if (e.target.classList.contains('message-checkbox')) {
        updateSelectedMessages();
    }
});

// Move to folder functionality
function showMoveToFolderModal(messageId) {
    currentMessageId = messageId;
    const modal = new bootstrap.Modal(document.getElementById('moveToFolderModal'));
    modal.show();
}

function confirmMoveToFolder() {
    const folderId = document.getElementById('target_folder').value;

    if (currentMessageId) {
        moveMessageToFolder(currentMessageId, folderId);
    }
}

function moveMessageToFolder(messageId, folderId) {
    const formData = new FormData();
    formData.append('message_id', messageId);
    formData.append('folder_id', folderId);

    fetch('<?= URLROOT ?>/admin/email_dashboard/moveToFolder', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('success', data.message);
            setTimeout(() => window.location.reload(), 1000);
        } else {
            showToast('error', data.message);
        }
    })
    .catch(error => {
        showToast('error', 'An error occurred while moving the message');
    });

    bootstrap.Modal.getInstance(document.getElementById('moveToFolderModal')).hide();
}

// Reminder functionality
function showReminderModal(messageId) {
    currentMessageId = messageId;
    const modal = new bootstrap.Modal(document.getElementById('reminderModal'));
    modal.show();
}

function setQuickReminder(minutes) {
    const now = new Date();
    now.setMinutes(now.getMinutes() + minutes);

    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const mins = String(now.getMinutes()).padStart(2, '0');

    document.getElementById('reminder_time').value = `${year}-${month}-${day}T${hours}:${mins}`;
}

function confirmSetReminder() {
    const reminderTime = document.getElementById('reminder_time').value;
    const note = document.getElementById('reminder_note').value;

    if (!reminderTime) {
        showToast('error', 'Please select a reminder time');
        return;
    }

    if (currentMessageId) {
        setMessageReminder(currentMessageId, reminderTime, note);
    }
}

function setMessageReminder(messageId, reminderTime, note) {
    const formData = new FormData();
    formData.append('message_id', messageId);
    formData.append('reminder_time', reminderTime);
    formData.append('note', note);

    fetch('<?= URLROOT ?>/admin/email_dashboard/setReminder', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('success', data.message);
            document.getElementById('reminder_time').value = '';
            document.getElementById('reminder_note').value = '';
        } else {
            showToast('error', data.message);
        }
    })
    .catch(error => {
        showToast('error', 'An error occurred while setting the reminder');
    });

    bootstrap.Modal.getInstance(document.getElementById('reminderModal')).hide();
}

// Bulk operations
function bulkMoveToFolder() {
    if (selectedMessages.length === 0) return;

    const folderId = prompt('Enter folder ID (or 0 for inbox):');
    if (folderId === null) return;

    selectedMessages.forEach(messageId => {
        moveMessageToFolder(messageId, folderId);
    });
}

function bulkSetReminder() {
    if (selectedMessages.length === 0) return;

    const reminderTime = prompt('Enter reminder time (YYYY-MM-DD HH:MM):');
    if (!reminderTime) return;

    selectedMessages.forEach(messageId => {
        setMessageReminder(messageId, reminderTime, '');
    });
}

function bulkMarkAsRead() {
    if (selectedMessages.length === 0) return;

    // Implementation would go here
    showToast('info', 'Bulk mark as read functionality coming soon');
}

// Delete folder
function deleteFolder(folderId) {
    if (confirm('Are you sure you want to delete this folder? Messages in this folder will be moved to the inbox.')) {
        // Implementation would go here
        showToast('info', 'Delete folder functionality coming soon');
    }
}

// Toast notifications
function showToast(type, message) {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type === 'error' ? 'danger' : type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">${message}</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;

    // Add to toast container
    let container = document.getElementById('toast-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'toast-container';
        container.className = 'toast-container position-fixed top-0 end-0 p-3';
        document.body.appendChild(container);
    }

    container.appendChild(toast);

    // Show toast
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();

    // Remove after hiding
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>
