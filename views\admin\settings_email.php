<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid px-2 px-md-4">
    <div class="row mb-4 align-items-center">
        <div class="col-md-6">
            <h1 class="display-5 fw-bold">Email Settings</h1>
            <p class="text-muted">Configure email server settings and notifications</p>
        </div>
        <div class="col-md-6 text-md-end mt-3 mt-md-0">
            <a href="<?php echo BASE_URL; ?>/admin/settings" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back to Settings
            </a>
        </div>
    </div>

    <?php if (isset($success) && $success) : ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <strong>Success!</strong> Email settings have been updated.
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-primary text-white py-3">
                    <h3 class="card-title mb-0"><i class="fas fa-envelope me-2"></i> SMTP Configuration</h3>
                </div>
                <div class="card-body p-4">
                    <form action="<?php echo BASE_URL; ?>/admin/settings_email" method="POST">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCsrfToken(); ?>">
                        
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email_smtp_host" class="form-label">SMTP Host</label>
                                    <input type="text" class="form-control" id="email_smtp_host" name="email_smtp_host" value="<?php echo $email_smtp_host; ?>" placeholder="e.g., smtp.gmail.com">
                                    <div class="form-text">The hostname of your SMTP server</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email_smtp_port" class="form-label">SMTP Port</label>
                                    <input type="text" class="form-control" id="email_smtp_port" name="email_smtp_port" value="<?php echo $email_smtp_port; ?>" placeholder="e.g., 587">
                                    <div class="form-text">Common ports: 25, 465 (SSL), 587 (TLS)</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email_smtp_username" class="form-label">SMTP Username</label>
                                    <input type="text" class="form-control" id="email_smtp_username" name="email_smtp_username" value="<?php echo $email_smtp_username; ?>" placeholder="Your email address or username">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email_smtp_password" class="form-label">SMTP Password</label>
                                    <input type="password" class="form-control" id="email_smtp_password" name="email_smtp_password" placeholder="<?php echo empty($email_smtp_password) ? 'Enter password' : 'Leave blank to keep current password'; ?>">
                                    <div class="form-text">Leave blank to keep the current password</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <label for="email_smtp_encryption" class="form-label">Encryption</label>
                            <select class="form-select" id="email_smtp_encryption" name="email_smtp_encryption">
                                <option value="tls" <?php echo $email_smtp_encryption == 'tls' ? 'selected' : ''; ?>>TLS</option>
                                <option value="ssl" <?php echo $email_smtp_encryption == 'ssl' ? 'selected' : ''; ?>>SSL</option>
                                <option value="" <?php echo $email_smtp_encryption == '' ? 'selected' : ''; ?>>None</option>
                            </select>
                            <div class="form-text">The encryption method used by your SMTP server</div>
                        </div>
                        
                        <hr class="my-4">
                        
                        <h4 class="mb-3">Sender Information</h4>
                        
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email_from_address" class="form-label">From Email Address</label>
                                    <input type="email" class="form-control <?php echo isset($email_from_address_err) ? 'is-invalid' : ''; ?>" id="email_from_address" name="email_from_address" value="<?php echo $email_from_address; ?>" placeholder="<EMAIL>">
                                    <?php if (isset($email_from_address_err)) : ?>
                                        <div class="invalid-feedback"><?php echo $email_from_address_err; ?></div>
                                    <?php endif; ?>
                                    <div class="form-text">The email address that will appear in the From field</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email_from_name" class="form-label">From Name</label>
                                    <input type="text" class="form-control" id="email_from_name" name="email_from_name" value="<?php echo $email_from_name; ?>" placeholder="Your Site Name">
                                    <div class="form-text">The name that will appear in the From field</div>
                                </div>
                            </div>
                        </div>
                        
                        <hr class="my-4">
                        
                        <h4 class="mb-3">Notification Settings</h4>
                        
                        <div class="form-check form-switch mb-4">
                            <input class="form-check-input" type="checkbox" id="email_notification_new_show" name="email_notification_new_show" <?php echo $email_notification_new_show == '1' ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="email_notification_new_show">Send notification when new show is created</label>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-save me-2"></i> Save Settings
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-info text-white py-3">
                    <h3 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i> Test Email Configuration</h3>
                </div>
                <div class="card-body p-4">
                    <p>You can test your email configuration by sending a test email to verify that everything is working correctly.</p>
                    
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i> Make sure to save your settings before testing.
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="test_email" class="form-label">Test Email Address</label>
                                <input type="email" class="form-control" id="test_email" placeholder="Enter email address to receive test">
                            </div>
                        </div>
                        <div class="col-md-6 d-flex align-items-end">
                            <button type="button" id="send_test_email" class="btn btn-info mb-3">
                                <i class="fas fa-paper-plane me-2"></i> Send Test Email
                            </button>
                        </div>
                    </div>
                    
                    <div id="test_result" class="mt-3" style="display: none;"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Test email functionality
    const sendTestBtn = document.getElementById('send_test_email');
    const testEmailInput = document.getElementById('test_email');
    const testResult = document.getElementById('test_result');
    
    sendTestBtn.addEventListener('click', function() {
        const email = testEmailInput.value.trim();
        
        if (!email) {
            showTestResult('error', 'Please enter a valid email address');
            return;
        }
        
        // Disable button and show loading
        sendTestBtn.disabled = true;
        sendTestBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Sending...';
        
        // Send AJAX request
        fetch('<?php echo BASE_URL; ?>/admin/testEmail', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-Token': '<?php echo generateCsrfToken(); ?>'
            },
            body: 'email=' + encodeURIComponent(email) + '&csrf_token=' + encodeURIComponent('<?php echo generateCsrfToken(); ?>')
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showTestResult('success', 'Test email sent successfully! Please check your inbox.');
            } else {
                showTestResult('error', 'Failed to send test email: ' + data.message);
            }
        })
        .catch(error => {
            showTestResult('error', 'An error occurred: ' + error.message);
        })
        .finally(() => {
            // Re-enable button
            sendTestBtn.disabled = false;
            sendTestBtn.innerHTML = '<i class="fas fa-paper-plane me-2"></i> Send Test Email';
        });
    });
    
    function showTestResult(type, message) {
        testResult.style.display = 'block';
        testResult.className = 'alert mt-3 ' + (type === 'success' ? 'alert-success' : 'alert-danger');
        testResult.innerHTML = message;
    }
});
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>