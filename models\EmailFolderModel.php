<?php

/**
 * Email Folder Management Model
 * Handles email folder operations for admin email management
 */
class EmailFolderModel {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Get all email folders
     */
    public function getAllFolders() {
        $this->db->query("SELECT f.*, u.name as created_by_name,
                         (SELECT COUNT(*) FROM messages WHERE folder_id = f.id AND is_archived = 0) as message_count
                         FROM email_folders f
                         LEFT JOIN users u ON f.created_by = u.id
                         ORDER BY f.is_system DESC, f.name ASC");
        return $this->db->resultSet();
    }
    
    /**
     * Get folder by ID
     */
    public function getFolderById($folderId) {
        $this->db->query("SELECT * FROM email_folders WHERE id = :folder_id");
        $this->db->bind(':folder_id', $folderId);
        return $this->db->single();
    }
    
    /**
     * Create new folder
     */
    public function createFolder($name, $description, $color, $icon, $createdBy) {
        $this->db->query("INSERT INTO email_folders (name, description, color, icon, created_by) 
                         VALUES (:name, :description, :color, :icon, :created_by)");
        $this->db->bind(':name', $name);
        $this->db->bind(':description', $description);
        $this->db->bind(':color', $color);
        $this->db->bind(':icon', $icon);
        $this->db->bind(':created_by', $createdBy);
        
        if ($this->db->execute()) {
            return $this->db->lastInsertId();
        }
        return false;
    }
    
    /**
     * Update folder
     */
    public function updateFolder($folderId, $name, $description, $color, $icon) {
        $this->db->query("UPDATE email_folders 
                         SET name = :name, description = :description, color = :color, icon = :icon
                         WHERE id = :folder_id AND is_system = 0");
        $this->db->bind(':folder_id', $folderId);
        $this->db->bind(':name', $name);
        $this->db->bind(':description', $description);
        $this->db->bind(':color', $color);
        $this->db->bind(':icon', $icon);
        
        return $this->db->execute();
    }
    
    /**
     * Delete folder (only non-system folders)
     */
    public function deleteFolder($folderId) {
        // First move all messages in this folder to Inbox
        $this->db->query("UPDATE messages SET folder_id = 1 WHERE folder_id = :folder_id");
        $this->db->bind(':folder_id', $folderId);
        $this->db->execute();
        
        // Then delete the folder
        $this->db->query("DELETE FROM email_folders WHERE id = :folder_id AND is_system = 0");
        $this->db->bind(':folder_id', $folderId);
        
        return $this->db->execute();
    }
    
    /**
     * Move message to folder
     */
    public function moveMessageToFolder($messageId, $folderId) {
        $this->db->query("UPDATE messages SET folder_id = :folder_id WHERE id = :message_id");
        $this->db->bind(':folder_id', $folderId);
        $this->db->bind(':message_id', $messageId);
        
        return $this->db->execute();
    }
    
    /**
     * Get messages in folder
     */
    public function getMessagesInFolder($folderId, $limit = 20, $offset = 0) {
        $this->db->query("SELECT m.*, u.name as from_user_name, s.name as show_title, f.name as folder_name
                         FROM messages m
                         LEFT JOIN users u ON m.from_user_id = u.id
                         LEFT JOIN shows s ON m.show_id = s.id
                         LEFT JOIN email_folders f ON m.folder_id = f.id
                         WHERE m.folder_id = :folder_id AND m.is_archived = 0
                         ORDER BY m.created_at DESC
                         LIMIT :limit OFFSET :offset");
        $this->db->bind(':folder_id', $folderId);
        $this->db->bind(':limit', $limit);
        $this->db->bind(':offset', $offset);
        
        return $this->db->resultSet();
    }
    
    /**
     * Get folder message count
     */
    public function getFolderMessageCount($folderId) {
        $this->db->query("SELECT COUNT(*) as count FROM messages WHERE folder_id = :folder_id AND is_archived = 0");
        $this->db->bind(':folder_id', $folderId);
        $result = $this->db->single();
        
        return $result ? (int)$result->count : 0;
    }
    
    /**
     * Get folder statistics
     */
    public function getFolderStatistics() {
        $this->db->query("SELECT f.id, f.name, f.color, f.icon, f.is_system,
                         COUNT(m.id) as message_count,
                         COUNT(CASE WHEN m.is_read = 0 THEN 1 END) as unread_count
                         FROM email_folders f
                         LEFT JOIN messages m ON f.id = m.folder_id AND m.is_archived = 0 AND m.message_type = 'email'
                         GROUP BY f.id, f.name, f.color, f.icon, f.is_system
                         ORDER BY f.is_system DESC, f.name ASC");
        return $this->db->resultSet();
    }
    
    /**
     * Search messages across folders
     */
    public function searchMessages($query, $folderId = null, $limit = 20, $offset = 0) {
        $sql = "SELECT m.*, u.name as from_user_name, s.name as show_title, f.name as folder_name
                FROM messages m
                LEFT JOIN users u ON m.from_user_id = u.id
                LEFT JOIN shows s ON m.show_id = s.id
                LEFT JOIN email_folders f ON m.folder_id = f.id
                WHERE m.is_archived = 0 
                AND (m.subject LIKE :query OR m.message LIKE :query OR m.original_sender_email LIKE :query)";
        
        if ($folderId) {
            $sql .= " AND m.folder_id = :folder_id";
        }
        
        $sql .= " ORDER BY m.created_at DESC LIMIT :limit OFFSET :offset";
        
        $this->db->query($sql);
        $this->db->bind(':query', '%' . $query . '%');
        if ($folderId) {
            $this->db->bind(':folder_id', $folderId);
        }
        $this->db->bind(':limit', $limit);
        $this->db->bind(':offset', $offset);
        
        return $this->db->resultSet();
    }
    
    /**
     * Get default folders
     */
    public function getDefaultFolders() {
        $this->db->query("SELECT * FROM email_folders WHERE is_system = 1 ORDER BY name ASC");
        return $this->db->resultSet();
    }
    
    /**
     * Bulk move messages to folder
     */
    public function bulkMoveMessages($messageIds, $folderId) {
        if (empty($messageIds)) {
            return false;
        }
        
        $placeholders = str_repeat('?,', count($messageIds) - 1) . '?';
        $this->db->query("UPDATE messages SET folder_id = ? WHERE id IN ($placeholders)");
        
        $this->db->bind(1, $folderId);
        foreach ($messageIds as $index => $messageId) {
            $this->db->bind($index + 2, $messageId);
        }
        
        return $this->db->execute();
    }
}
