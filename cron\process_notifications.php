<?php
/**
 * Notification Processing Cron Script
 * 
 * This script processes pending notifications and should be run every 5 minutes via cron.
 * 
 * Usage:
 * 0,5,10,15,20,25,30,35,40,45,50,55 * * * * /usr/bin/php /path/to/your/site/cron/process_notifications.php
 * 
 * Or via curl:
 * 0,5,10,15,20,25,30,35,40,45,50,55 * * * * curl -s "https://yoursite.com/notification/process?key=DAILY_KEY"
 */

// Set the script to run from command line or web
if (php_sapi_name() !== 'cli') {
    // If running via web, check for security key
    $cronKey = $_GET['key'] ?? '';
    $expectedKey = hash('sha256', 'notification_cron_' . gmdate('Y-m-d'));
    
    if ($cronKey !== $expectedKey) {
        http_response_code(403);
        echo "Unauthorized access";
        exit;
    }
}

// Include the application bootstrap
require_once dirname(__DIR__) . '/config/config.php';
require_once dirname(__DIR__) . '/core/Database.php';
require_once dirname(__DIR__) . '/models/NotificationModel.php';
require_once dirname(__DIR__) . '/models/NotificationService.php';
require_once dirname(__DIR__) . '/models/EmailService.php';
require_once dirname(__DIR__) . '/models/SettingsModel.php';

// Set error reporting for cron jobs
if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    ini_set('log_errors', 1);
    ini_set('error_log', dirname(__DIR__) . '/logs/cron_errors.log');
}

// Function to log messages
function logMessage($message, $level = 'INFO') {
    $timestamp = gmdate('Y-m-d H:i:s');
    $logMessage = "[$timestamp] [$level] $message" . PHP_EOL;
    
    if (DEBUG_MODE) {
        echo $logMessage;
    }
    
    // Ensure logs directory exists
    $logsDir = dirname(__DIR__) . '/logs';
    if (!is_dir($logsDir)) {
        mkdir($logsDir, 0755, true);
    }
    
    // Log to file
    $logFile = $logsDir . '/notification_cron.log';
    
    // Check if we can write to the log file
    if (!is_writable($logsDir)) {
        if (DEBUG_MODE) {
            echo "WARNING: Cannot write to logs directory: $logsDir" . PHP_EOL;
        }
        return;
    }
    
    $result = file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    
    if ($result === false && DEBUG_MODE) {
        echo "WARNING: Failed to write to log file: $logFile" . PHP_EOL;
    }
}

try {
    logMessage("Starting notification processing cron job");
    logMessage("PHP Version: " . PHP_VERSION);
    logMessage("Memory Limit: " . ini_get('memory_limit'));
    logMessage("Max Execution Time: " . ini_get('max_execution_time'));
    logMessage("Current Working Directory: " . getcwd());
    logMessage("Script Path: " . __FILE__);
    
    // Check if required classes exist
    if (!class_exists('Database')) {
        throw new Exception('Database class not found');
    }
    
    if (!class_exists('NotificationModel')) {
        throw new Exception('NotificationModel class not found');
    }
    
    if (!class_exists('NotificationService')) {
        throw new Exception('NotificationService class not found');
    }
    
    logMessage("All required classes loaded successfully");
    
    // Test database connection
    try {
        $testDb = new Database();
        logMessage("Database connection test successful");
    } catch (Exception $e) {
        throw new Exception("Database connection failed: " . $e->getMessage());
    }
    
    // Initialize the notification service
    logMessage("Initializing NotificationService");
    $notificationService = new NotificationService();
    logMessage("NotificationService initialized successfully");
    
    // Check for pending notifications before processing
    $db = new Database();
    $db->query('SELECT COUNT(*) as count FROM notification_queue WHERE status = ?');
    $db->bind(1, 'pending');
    $db->execute();
    $result = $db->single();
    $pendingCount = $result->count ?? 0;
    logMessage("Found $pendingCount pending notifications");
    
    // Initialize results array
    $results = [
        'processed' => 0,
        'sent' => 0,
        'failed' => 0,
        'errors' => []
    ];
    
    if ($pendingCount == 0) {
        logMessage("No pending notifications to process");
    } else {
        // Process pending notifications (limit to 100 per run to avoid timeouts)
        logMessage("Processing up to 100 pending notifications");
        $results = $notificationService->processPendingNotifications(100);
        
        logMessage("Processing complete - Processed: {$results['processed']}, Sent: {$results['sent']}, Failed: {$results['failed']}");
        
        // Log any errors
        if (!empty($results['errors'])) {
            logMessage("Errors encountered during processing:");
            foreach ($results['errors'] as $error) {
                logMessage($error, 'ERROR');
            }
        }
    }
    
    // Clean up old notifications (older than 30 days)
    $db = new Database();
    $db->query('DELETE FROM notification_queue WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)');
    $cleanupResult = $db->execute();
    
    if ($cleanupResult) {
        $affectedRows = $db->rowCount();
        if ($affectedRows > 0) {
            logMessage("Cleaned up $affectedRows old notifications");
        }
    }
    
    // Clean up old push/toast notifications (older than 7 days and read)
    $db->query('DELETE FROM user_push_notifications WHERE created_at < DATE_SUB(NOW(), INTERVAL 7 DAY) AND is_read = 1');
    $db->execute();
    
    $db->query('DELETE FROM user_toast_notifications WHERE created_at < DATE_SUB(NOW(), INTERVAL 7 DAY) AND is_read = 1');
    $db->execute();
    
    // Update heartbeat file to track last successful run
    $heartbeatFile = dirname(__DIR__) . '/logs/cron_heartbeat.txt';
    $heartbeatData = [
        'last_run' => gmdate('Y-m-d H:i:s'),
        'status' => 'success',
        'processed' => $results['processed'],
        'sent' => $results['sent'],
        'failed' => $results['failed'],
        'pending_count' => $pendingCount,
        'errors' => $results['errors']
    ];
    
    // Ensure logs directory exists
    $logsDir = dirname($heartbeatFile);
    if (!is_dir($logsDir)) {
        mkdir($logsDir, 0755, true);
    }
    
    $heartbeatWritten = file_put_contents($heartbeatFile, json_encode($heartbeatData, JSON_PRETTY_PRINT));
    
    if ($heartbeatWritten === false) {
        logMessage("Failed to write heartbeat file: $heartbeatFile", 'ERROR');
    } else {
        logMessage("Heartbeat file written successfully: $heartbeatFile");
    }
    
    logMessage("Notification processing cron job completed successfully");
    logMessage("Heartbeat file path: " . $heartbeatFile);
    logMessage("Heartbeat data: " . json_encode($heartbeatData));
    
    // Verify heartbeat file was created
    if (file_exists($heartbeatFile)) {
        logMessage("Heartbeat file exists and is readable");
        $fileSize = filesize($heartbeatFile);
        logMessage("Heartbeat file size: $fileSize bytes");
    } else {
        logMessage("WARNING: Heartbeat file was not created!", 'ERROR');
    }
    
    // If running via web, return JSON response
    if (php_sapi_name() !== 'cli') {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'processed' => $results['processed'] ?? 0,
            'sent' => $results['sent'] ?? 0,
            'failed' => $results['failed'] ?? 0,
            'errors' => $results['errors'] ?? [],
            'pending_count' => $pendingCount ?? 0,
            'timestamp' => gmdate('Y-m-d H:i:s')
        ]);
    }
    
} catch (Exception $e) {
    $errorMessage = "Cron job failed: " . $e->getMessage();
    logMessage($errorMessage, 'ERROR');
    
    if (DEBUG_MODE) {
        logMessage("Stack trace: " . $e->getTraceAsString(), 'ERROR');
    }
    
    // Update heartbeat file with error status
    $heartbeatFile = dirname(__DIR__) . '/logs/cron_heartbeat.txt';
    $heartbeatData = [
        'last_run' => gmdate('Y-m-d H:i:s'),
        'status' => 'error',
        'error' => $errorMessage,
        'processed' => 0,
        'sent' => 0,
        'failed' => 0,
        'pending_count' => 0,
        'errors' => [$errorMessage]
    ];
    
    // Ensure logs directory exists
    $logsDir = dirname($heartbeatFile);
    if (!is_dir($logsDir)) {
        mkdir($logsDir, 0755, true);
    }
    
    $heartbeatWritten = file_put_contents($heartbeatFile, json_encode($heartbeatData, JSON_PRETTY_PRINT));
    
    if ($heartbeatWritten === false) {
        error_log("Failed to write error heartbeat file: $heartbeatFile");
    }
    
    // If running via web, return error response
    if (php_sapi_name() !== 'cli') {
        http_response_code(500);
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'error' => $errorMessage,
            'timestamp' => gmdate('Y-m-d H:i:s')
        ]);
    }
    
    exit(1);
}

exit(0);
?>