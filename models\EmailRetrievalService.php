<?php
/**
 * Email Retrieval Service
 * 
 * Handles POP3/IMAP email fetching for the unified messaging system
 * Supports both POP3 and IMAP protocols with SSL/TLS encryption
 */
class EmailRetrievalService {
    private $db;
    private $settingsModel;
    private $connection;
    private $protocol;
    private $host;
    private $port;
    private $username;
    private $password;
    private $encryption;
    private $deleteAfterProcessing;
    
    public function __construct() {
        $this->db = new Database();
        require_once APPROOT . '/models/SettingsModel.php';
        $this->settingsModel = new SettingsModel();
        $this->loadSettings();
    }
    
    /**
     * Load email server settings from database
     */
    private function loadSettings() {
        $this->protocol = $this->settingsModel->getSetting('email_server_protocol', 'imap');
        $this->host = $this->settingsModel->getSetting('email_server_host', '');
        $this->port = $this->settingsModel->getSetting('email_server_port', '993');
        $this->username = $this->settingsModel->getSetting('email_server_username', '');
        $this->password = $this->settingsModel->getSetting('email_server_password', '');
        $this->encryption = $this->settingsModel->getSetting('email_server_encryption', 'ssl');
        $this->deleteAfterProcessing = $this->settingsModel->getSetting('email_delete_after_processing', '1') === '1';
    }
    
    /**
     * Check if email retrieval is properly configured
     */
    public function isConfigured() {
        return !empty($this->host) && !empty($this->username) && !empty($this->password);
    }
    
    /**
     * Test email server connection
     */
    public function testConnection() {
        if (!$this->isConfigured()) {
            return ['success' => false, 'message' => 'Email server not configured'];
        }
        
        try {
            $this->connect();
            $this->disconnect();
            return ['success' => true, 'message' => 'Connection successful'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Connection failed: ' . $e->getMessage()];
        }
    }
    
    /**
     * Connect to email server
     */
    private function connect() {
        if ($this->connection) {
            return true;
        }
        
        if (!$this->isConfigured()) {
            throw new Exception('Email server not configured');
        }
        
        // Build connection string
        $connectionString = '{' . $this->host . ':' . $this->port;
        
        if ($this->protocol === 'imap') {
            $connectionString .= '/imap';
        } else {
            $connectionString .= '/pop3';
        }
        
        if ($this->encryption === 'ssl') {
            $connectionString .= '/ssl';
        } elseif ($this->encryption === 'tls') {
            $connectionString .= '/tls';
        }
        
        $connectionString .= '/novalidate-cert}INBOX';
        
        // Attempt connection
        $this->connection = imap_open($connectionString, $this->username, $this->password);
        
        if (!$this->connection) {
            throw new Exception('Failed to connect to email server: ' . imap_last_error());
        }
        
        return true;
    }
    
    /**
     * Disconnect from email server
     */
    private function disconnect() {
        if ($this->connection) {
            imap_close($this->connection);
            $this->connection = null;
        }
    }
    
    /**
     * Fetch new emails from server
     */
    public function fetchEmails($limit = 50) {
        try {
            $this->connect();
            
            // Get message count
            $messageCount = imap_num_msg($this->connection);
            
            if ($messageCount === 0) {
                $this->disconnect();
                return ['success' => true, 'emails' => [], 'message' => 'No new emails'];
            }
            
            // Calculate range to fetch (newest first)
            $start = max(1, $messageCount - $limit + 1);
            $end = $messageCount;
            
            $emails = [];
            
            for ($i = $end; $i >= $start; $i--) {
                try {
                    $email = $this->parseEmail($i);
                    if ($email) {
                        $emails[] = $email;
                    }
                } catch (Exception $e) {
                    error_log("EmailRetrievalService::fetchEmails - Error parsing email $i: " . $e->getMessage());
                    continue;
                }
            }
            
            $this->disconnect();
            
            return [
                'success' => true, 
                'emails' => $emails, 
                'message' => 'Fetched ' . count($emails) . ' emails'
            ];
            
        } catch (Exception $e) {
            $this->disconnect();
            error_log("EmailRetrievalService::fetchEmails - Error: " . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * Parse individual email
     */
    private function parseEmail($messageNumber) {
        $header = imap_headerinfo($this->connection, $messageNumber);
        $body = imap_body($this->connection, $messageNumber);
        
        if (!$header) {
            throw new Exception("Failed to get email header for message $messageNumber");
        }
        
        // Extract basic information
        $email = [
            'message_number' => $messageNumber,
            'message_id' => $header->message_id ?? '',
            'date' => isset($header->date) ? date('Y-m-d H:i:s', strtotime($header->date)) : date('Y-m-d H:i:s'),
            'subject' => $this->decodeHeader($header->subject ?? ''),
            'from_email' => $this->extractEmail($header->from ?? []),
            'from_name' => $this->extractName($header->from ?? []),
            'to_email' => $this->extractEmail($header->to ?? []),
            'reply_to' => $this->extractEmail($header->reply_to ?? []),
            'body' => $this->decodeBody($body),
            'size' => $header->Size ?? 0,
            'attachments' => [],
            'is_multipart' => false
        ];
        
        // Check for multipart message (attachments)
        $structure = imap_fetchstructure($this->connection, $messageNumber);
        if (isset($structure->parts) && count($structure->parts) > 0) {
            $email['is_multipart'] = true;
            $email['attachments'] = $this->extractAttachments($messageNumber, $structure);
        }
        
        // Extract threading information
        $email['in_reply_to'] = $header->in_reply_to ?? '';
        $email['references'] = $header->references ?? '';
        
        return $email;
    }
    
    /**
     * Decode email header (handles encoding)
     */
    private function decodeHeader($header) {
        $decoded = imap_mime_header_decode($header);
        $result = '';
        
        foreach ($decoded as $part) {
            $result .= $part->text;
        }
        
        return trim($result);
    }
    
    /**
     * Extract email address from header object
     */
    private function extractEmail($headerArray) {
        if (empty($headerArray) || !is_array($headerArray)) {
            return '';
        }
        
        $first = $headerArray[0];
        if (isset($first->mailbox) && isset($first->host)) {
            return $first->mailbox . '@' . $first->host;
        }
        
        return '';
    }
    
    /**
     * Extract name from header object
     */
    private function extractName($headerArray) {
        if (empty($headerArray) || !is_array($headerArray)) {
            return '';
        }
        
        $first = $headerArray[0];
        return isset($first->personal) ? $this->decodeHeader($first->personal) : '';
    }
    
    /**
     * Decode email body
     */
    private function decodeBody($body) {
        // Handle different encodings
        $decoded = quoted_printable_decode($body);
        
        // Remove excessive whitespace and normalize line endings
        $decoded = preg_replace('/\r\n|\r|\n/', "\n", $decoded);
        $decoded = preg_replace('/\n{3,}/', "\n\n", $decoded);
        
        return trim($decoded);
    }
    
    /**
     * Extract attachments from multipart message
     */
    private function extractAttachments($messageNumber, $structure) {
        $attachments = [];
        
        if (!isset($structure->parts)) {
            return $attachments;
        }
        
        foreach ($structure->parts as $partNumber => $part) {
            if (isset($part->disposition) && strtolower($part->disposition) === 'attachment') {
                $attachment = [
                    'part_number' => $partNumber + 1,
                    'filename' => '',
                    'size' => $part->bytes ?? 0,
                    'type' => $part->type ?? 0,
                    'subtype' => $part->subtype ?? '',
                    'encoding' => $part->encoding ?? 0
                ];
                
                // Extract filename
                if (isset($part->dparameters)) {
                    foreach ($part->dparameters as $param) {
                        if (strtolower($param->attribute) === 'filename') {
                            $attachment['filename'] = $this->decodeHeader($param->value);
                            break;
                        }
                    }
                }
                
                if (empty($attachment['filename']) && isset($part->parameters)) {
                    foreach ($part->parameters as $param) {
                        if (strtolower($param->attribute) === 'name') {
                            $attachment['filename'] = $this->decodeHeader($param->value);
                            break;
                        }
                    }
                }
                
                $attachments[] = $attachment;
            }
        }
        
        return $attachments;
    }
    
    /**
     * Download attachment content
     */
    public function downloadAttachment($messageNumber, $partNumber, $encoding) {
        if (!$this->connection) {
            throw new Exception('Not connected to email server');
        }
        
        $data = imap_fetchbody($this->connection, $messageNumber, $partNumber);
        
        // Decode based on encoding
        switch ($encoding) {
            case 1: // 8bit
                break;
            case 2: // binary
                break;
            case 3: // base64
                $data = base64_decode($data);
                break;
            case 4: // quoted-printable
                $data = quoted_printable_decode($data);
                break;
        }
        
        return $data;
    }
    
    /**
     * Mark email for deletion
     */
    public function markForDeletion($messageNumber) {
        if (!$this->connection) {
            throw new Exception('Not connected to email server');
        }
        
        return imap_delete($this->connection, $messageNumber);
    }
    
    /**
     * Expunge deleted emails
     */
    public function expungeDeleted() {
        if (!$this->connection) {
            throw new Exception('Not connected to email server');
        }
        
        return imap_expunge($this->connection);
    }
    
    /**
     * Destructor - ensure connection is closed
     */
    public function __destruct() {
        $this->disconnect();
    }
}
