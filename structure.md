# Events and Shows Management System - Project Structure

This document outlines the structure of the Events and Shows Management System, providing a brief description of each folder and its purpose.

## Root Directories

- `/controllers` - Contains controller classes that handle user requests and coordinate between models and views
- `/models` - Contains model classes that interact with the database and implement business logic
- `/views` - Contains view templates that render the user interface
  - `/views/admin` - Admin dashboard views (accessible at `/admin/dashboard` and `/admin/settings`)
    - `/views/admin/shows.php` - Comprehensive show management dashboard with filtering, sorting, and bulk operations
- `/core` - Contains core framework files and base classes
- `/helpers` - Contains helper functions and utility classes
  - `/helpers/format_helper.php` - Enhanced with timeAgo() function for relative timestamps
- `/libraries` - Contains third-party libraries and custom libraries
- `/uploads` - Storage for user-uploaded files (images, documents, etc.)
- `/autobackup` - Automatic backups of modified files organized by date and time
- `/sql` - SQL scripts for database creation and updates
- `/docs` - Documentation files for various system components
- `/test` - Contains test scripts and one-time migration scripts that can be deleted after use
- `/scripts` - Contains cron job scripts and scheduled tasks

## Root Documentation Files

- `SYSTEM_INIT.md` - Guide for properly initializing the system in scripts and applications
- `structure.md` - Overview of the project structure and organization
- `features.md` - List of implemented features and their status
- `task_pending.md` - List of features to be developed
- `CHANGELOG.md` - Record of all notable changes to the system
- `CRITICAL_FIXES_DO_NOT_REMOVE.md` - ⚠️ Critical fixes that must not be removed or modified
- `EVENT_CHART_DEVELOPMENT_SUMMARY.md` - Comprehensive development history of the Event chart component
- `DEBUG_CODE_CLEANUP.md` - ⚠️ Documentation of debug code requiring cleanup for production

## Controller Structure

- `/controllers/AdminController.php` - Handles admin-specific functionality
  - Comprehensive show management dashboard at `/admin/shows`
  - Statistics tracking and reporting
  - Bulk operations for show management
  - Recent activities monitoring
  - Upcoming deadlines tracking
- `/controllers/CoordinatorController.php` - Handles coordinator-specific functionality
- `/controllers/JudgeController.php` - Handles judge-specific functionality
- `/controllers/StaffController.php` - Handles staff-specific functionality
- `/controllers/UserController.php` - Handles user account management
- `/controllers/ShowController.php` - Handles show management
- `/controllers/RegistrationController.php` - Handles vehicle registrations
- `/controllers/PaymentController.php` - Handles payment processing
- `/controllers/ImageEditorController.php` - Handles image management
  - Provides methods for uploading, editing, and managing images
  - Implements `setPrimary()` and `setBanner()` methods for show image management
- `/controllers/PWAController.php` - Handles Progressive Web App functionality and FCM OAuth v1 notifications
  - FCM OAuth v1 token management (removed dead Web Push API code)
  - Offline sync and cached data handling
  - QR code scanning and processing
  - **Camera photo upload integration** - Direct upload to image editor (v3.63.21)
- `/controllers/NotificationCenterController.php` - Handles unified messaging system
  - Main message interface and conversation threading
  - AJAX endpoints for real-time message updates
  - Message viewing, archiving, and reply functionality
  - Unified inbox for all communication types
- `/controllers/CalendarController.php` - Handles calendar and event management
  - Provides methods for creating, editing, and managing calendars and events
  - Implements venue and club management functionality
  - Supports event import/export and integration with shows
  - Includes automatic address geocoding for events and venues
  - Enhanced with comprehensive calendar improvements (v3.37.0)
  - Monthly Event Chart view with mobile-first responsive design (v3.38.0)
  - Advanced filter system integration with Event chart (v3.46.0)

## Model Structure

- `/models/Database.php` - Database connection and query handling
  - **Enhanced with timezone helper functions** (v3.62.4)
  - Provides formatDateTimeForUser() and convertUTCToUserDateTime() globally
  - Ensures consistent timezone handling across all database operations
- `/models/UserModel.php` - User account data and operations
  - Contains user authentication, profile management, and advanced user search functionality
  - Implements `searchUsers()` and `advancedUserSearch()` methods for flexible user lookup
  - Supports case-insensitive and format-agnostic searching for names, emails, and phone numbers
- `/models/ShowModel.php` - Show data and operations
  - Includes methods for managing show data, categories, and images
  - Implements `updateFeaturedImage()` and `updateShowBannerImage()` for show image management
- `/models/RegistrationModel.php` - Registration data and operations
- `/models/VehicleModel.php` - Vehicle data and operations
- `/models/CategoryModel.php` - Category data and operations
- `/models/JudgingModel.php` - Judging data and operations
- `/models/PaymentModel.php` - Payment data and operations
- `/models/StaffModel.php` - Staff assignment and management operations
- `/models/ImageEditorModel.php` - Image management operations
- `/models/CustomFieldRetriever.php` - Custom field mapping and retrieval
  - **Enhanced with timezone preservation** (v3.62.4)
  - Skips date fields that require timezone conversion
  - Prevents overwriting of controller-provided timezone-converted values
  - Handles image uploads, optimization, and management for various entities
  - Implements `setPrimaryImage()` method to manage primary images for shows and vehicles
- `/models/CalendarModel.php` - Calendar and event data operations
  - Manages calendars, events, venues, and clubs
  - Implements methods for event recurrence, import/export, and notifications
  - Provides integration with shows to display show data in calendar format
- `/models/UnifiedMessageModel.php` - **Unified messaging system** (v3.64.0)
  - Consolidates all messaging functionality into single interface
  - Handles direct messages, system notifications, and judge communications
  - Supports multiple delivery methods (email, SMS, push, toast, in-app)
  - Features conversation threading and reply system
  - Tracks delivery status and user preferences
  - **Immediate email processing with fallback** (v3.64.5)
    - Instant email delivery when SMTP is working
    - Smart fallback to queue when immediate delivery fails
    - Beautiful HTML email formatting with professional styling
    - Contact form priority override (bypasses user notification preferences)
    - PHP server time consistency for reliable scheduling
- `/models/EmailService.php` - **Email delivery service** (v3.64.5)
  - SMTP configuration and email sending functionality
  - Immediate delivery with automatic fallback to queue
  - HTML and plain text email support
  - Smart error detection for connection vs configuration issues
  - Enhanced delivery tracking and comprehensive error handling
- `/models/NotificationService.php` - **Notification processing service** (v3.64.5)
  - Processes pending notifications from queue
  - Multi-channel delivery coordination (email, SMS, push, toast)
  - User preference handling with contact form override
  - Enhanced logging and debugging capabilities
  - Replaces complex notification system with clean, unified solution

## View Structure

- `/views/admin/` - Admin interface views
  - `/views/admin/dashboard.php` - Main admin dashboard (accessible at `/admin/dashboard`)
  - `/views/admin/settings.php` - Admin settings page (accessible at `/admin/settings`)
  - `/views/admin/settings_developer.php` - Developer tools and cache management (accessible at `/admin/settings_developer`)
  - `/views/admin/run_scripts.php` - Script runner interface (accessible at `/admin/runScripts`)
- `/views/coordinator/` - Coordinator interface views
- `/views/judge/` - Judge interface views
- `/views/staff/` - Staff interface views
- `/views/user/` - User account management views
- `/views/show/` - Show management views
- `/views/registration/` - Registration management views
- `/views/payments/` - Payment processing views
- `/views/calendar/` - Calendar and event management views
  - `/views/calendar/index.php` - Legacy calendar view with FullCalendar integration (no longer used)
  - `/views/calendar/custom_index.php` - Main calendar view with custom-built calendar implementation
  - `/views/calendar/custom_index_fixed.php` - Enhanced calendar view with comprehensive improvements (v3.37.0)
    - Includes event table below calendar with comprehensive event listing
    - Features AJAX hover popup with 500ms show/300ms hide delays
    - Implements proper event spacing and continuous scrolling animations
  - `/views/calendar/event.php` - Event details view
  - `/views/calendar/create_event.php` - Create event form
  - `/views/calendar/edit_event.php` - Edit event form
  - `/views/calendar/manage_calendars.php` - Calendar management interface
  - `/views/calendar/manage_venues.php` - Venue management interface
  - `/views/calendar/manage_clubs.php` - Club management interface
  - `/views/calendar/settings.php` - Calendar settings page
  - `/views/calendar/import.php` - Event import interface
- `/views/notification_center/` - **Unified messaging system views** (v3.64.0)
  - `/views/notification_center/index.php` - Main message interface with unified inbox
  - `/views/notification_center/view.php` - Individual message view with threading
  - Features conversation grouping, reply system, and real-time updates
  - Replaces complex notification center with clean, user-friendly interface
- `/views/includes/` - Reusable view components (header, footer, etc.)

## Public Assets

- `/public/css/` - Stylesheets for the application
  - `/public/css/custom-calendar.css` - Enhanced calendar styling with comprehensive improvements (v3.37.0)
    - Includes continuous scrolling animations for event titles
    - Features proper event spacing and positioning
    - Implements all-day and timed event sections
    - Provides event table and hover popup styling
  - `/public/css/wysiwyg-editor.css` - Secure WYSIWYG Editor styling (v3.47.0)
    - Modern toolbar design with responsive layout
    - Content area styling with proper typography
    - Image wrapper and deletion controls
    - Dark mode and print media support
  - `/public/css/club-search.css` - Club Search component styling (v3.47.0)
    - Search input and dropdown results styling
    - Selected clubs display with removal controls
    - Loading states and error message styling
    - Mobile-responsive design with accessibility features
- `/public/js/` - JavaScript files for client-side functionality
  - `/public/js/timezone-helper.js` - Comprehensive timezone utility system (v1.0.0) **NEW**
    - UTC date parsing for MySQL datetime format
    - Manual time/date formatting to avoid browser locale issues
    - Helper functions for consistent date handling across all JavaScript
    - Test event creation utilities with proper timezone handling
    - Integration with server-side PHP timezone helper functions
  - `/public/js/custom-calendar.js` - Enhanced calendar JavaScript implementation (v3.37.0)
    - **UPDATED**: UTC parsing for database datetime strings with timezone helper integration
    - Features time-based event positioning within day cells
    - Implements event table synchronization and hover popup functionality
    - Provides comprehensive event rendering with proper spacing
    - Includes continuous right-to-left scrolling animation for long titles
  - `/public/js/calendar-filters.js` - Advanced calendar filtering functionality (v3.47.0)
    - **UPDATED**: Timezone-safe timestamp generation for cache busting
    - Supports all calendar views including Monthly Event Chart
    - Provides comprehensive filtering options (calendars, dates, locations, categories, tags, prices)
    - Features infinite loop prevention for Event chart integration
    - Includes extensive debug logging for troubleshooting
  - `/public/js/monthly-event-chart.js` - Monthly Event Chart implementation (v3.48.0)
    - **UPDATED**: Timezone-safe time and date formatting with manual formatting methods
    - Weekly-based layout system for optimal screen utilization
    - Full filter system integration with compatibility methods
    - Drag and drop functionality with visual feedback
    - Desktop-focused design (mobile cards removed for streamlined experience)
    - Sunday-to-Saturday calendar layout with blank day support
    - Car show badge integration with gold car icons in spanner bars
    - Enhanced hover popups with car show indicators
  - `/public/js/wysiwyg-editor.js` - Secure WYSIWYG Editor for event descriptions (v3.47.0)
    - Self-hosted rich text editor with image upload capabilities
    - Base64 image storage with configurable limits and file type restrictions
    - Comprehensive toolbar with formatting, alignment, lists, tables, and colors
    - Content sanitization to prevent XSS attacks
    - Mobile-responsive design with touch-friendly controls
  - `/public/js/club-search.js` - Club Search and Management System (v3.47.0)
    - Real-time club search with autocomplete functionality
    - Quick club creation from search interface
    - Multiple club selection with visual feedback
    - AJAX-powered search with debouncing and error handling
    - Mobile-responsive design with accessibility features
  - `/public/js/pwa-features.js` - Progressive Web App Features (v3.63.21)
    - Camera integration for vehicle photo capture
    - QR code scanning functionality with banner rotation
    - Banner rotation system for both camera and QR scanner modals
    - Mobile-first responsive camera interface
    - PWA-specific functionality and offline capabilities
    - **Direct image editor upload integration** - Camera photos upload directly to image editor (v3.63.21)
    - **Contains debug code requiring cleanup for production** ⚠️
  - `/public/js/notification-center.js` - **Unified messaging system JavaScript** (v3.64.0)
    - Real-time message count updates and badge management
    - Desktop and mobile notification bell color changes
    - Periodic polling for new messages (every 30 seconds)
    - Clean badge positioning and styling without flashing
    - Integration with unified messaging backend

## Helper Files

- `/helpers/format_helper.php` - Functions for formatting data (currency, dates, etc.)
- `/helpers/fcm_v1_helper.php` - FCM HTTP v1 API with OAuth 2.0 authentication for push notifications
- `/helpers/csrf_helper.php` - Functions for CSRF protection
- `/helpers/validation_helper.php` - Functions for input validation
- `/helpers/image_helper.php` - Functions for image processing
- `/helpers/form_helper.php` - Functions for form rendering and processing
- `/helpers/geocoding_helper.php` - Functions for geocoding addresses to coordinates

## Upload Directories

- `/uploads/vehicles/` - Vehicle images (including PWA camera captures)
- `/uploads/shows/` - Show images
- `/uploads/events/` - Event images
- `/uploads/users/` - User profile images (including Facebook profile images)
- `/uploads/qrcodes/` - Generated QR codes

## Script Directories

- `/test/` - Contains test scripts and one-time migration scripts that can be deleted after use
  - `/test/migrate_facebook_profile_images.php` - One-time script to migrate Facebook profile images from users table to images table
- `/scripts/` - Contains cron job scripts and scheduled tasks
- `/core/` - Contains core functionality scripts that the site needs to function

## Documentation

- `/docs/scoring_system.md` - Documentation for the judging and scoring system
- `/docs/vehicle_scoring_model_explained.md` - Detailed explanation of vehicle scoring
- `/docs/listing_fee_system.md` - Documentation for the listing fee system
- `/docs/hierarchical_role_system.md` - Documentation for the role-based access control
- `/docs/staff_role_system.md` - Documentation for the staff role system
- `/docs/staff_role_database_setup.md` - Installation instructions for staff role database tables
- `/docs/registration_search.md` - Documentation for the advanced registration search functionality
- `/docs/staff_registration_interface.md` - Documentation for the staff registration interface
- `/docs/facebook_profile_sync.md` - Documentation for the Facebook profile image sync functionality
- `/docs/script_placement_guidelines.md` - Guidelines for placing scripts in appropriate directories
- `/docs/geocoding_system.md` - Documentation for the address geocoding system and recent improvements
- `/autobackup/facebook_profile_sync/FIXES_2024_08_19.md` - Documentation for the Facebook profile image sync fix
- `/autobackup/geocoding_fix_v3/SOLUTION.md` - Documentation for the geocoding system database fix

## Database Structure

- `/database/calendar.sql` - SQL script for creating calendar-related tables
  - `calendars` - Stores calendar information
  - `calendar_events` - Stores event information
  - `calendar_venues` - Stores venue information
  - `calendar_clubs` - Stores club/group information
  - `calendar_club_members` - Stores club membership information
  - `calendar_event_clubs` - Stores event-club relationships
  - `calendar_event_images` - Stores Base64 encoded images for events (v3.48.0)
  - `calendar_permissions` - Stores calendar permission settings
  - `calendar_notifications` - Stores event notification settings
  - `calendar_imports` - Stores import source information
  - `calendar_settings` - Stores calendar system settings
- `/database/migrations/create_unified_message_system.sql` - **Unified messaging system database** (v3.64.0)
  - `messages` - Core message storage with threading support
  - `message_deliveries` - Tracks delivery attempts and status
  - `notification_settings` - Global system settings
  - `user_notification_preferences` - Individual user preferences
  - `fcm_tokens` - Firebase Cloud Messaging tokens
  - Migrates data from legacy notification tables

## Configuration Files

- `/config/config.php` - Main configuration file
- `/config/routes.php` - URL routing configuration
- `/config/database.php` - Database connection configuration
- `/config/firebase-service-account.json` - Firebase service account credentials for FCM OAuth v1 API

## Core Framework Files

- `/core/App.php` - Main application class
- `/core/Controller.php` - Base controller class
- `/core/Model.php` - Base model class
- `/core/Database.php` - Database connection and query handling
- `/core/Router.php` - URL routing and request handling