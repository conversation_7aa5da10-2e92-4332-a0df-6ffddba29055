<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?php echo URLROOT; ?>/coordinator/dashboard">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?php echo URLROOT; ?>/coordinator/show/<?php echo $data['show']->id; ?>"><?php echo $data['show']->name; ?></a></li>
                    <li class="breadcrumb-item active" aria-current="page">Registrations</li>
                </ol>
            </nav>
            
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Manage Registrations - <?php echo $data['show']->name; ?></h5>
                    <div>
                        <button type="button" class="btn btn-success btn-sm me-2" onclick="openBulkMessageModal()">
                            <i class="fas fa-envelope me-1"></i>
                            <span class="d-none d-md-inline">Message All</span>
                            <span class="d-md-none">Message</span>
                        </button>
                        <a href="<?php echo URLROOT; ?>/coordinator/registerVehicleForUser/<?php echo $data['show']->id; ?>" class="btn btn-light btn-sm">
                            <i class="fas fa-plus"></i>
                            <span class="d-none d-md-inline">Register Vehicle</span>
                            <span class="d-md-none">Register</span>
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php if(empty($data['registrations'])) : ?>
                        <div class="alert alert-info">
                            No registrations found for this show.
                        </div>
                    <?php else : ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Reg #</th>
                                        <th>Vehicle</th>
                                        <th>Owner</th>
                                        <th>Category</th>
                                        <th>Status</th>
                                        <th>Payment</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach($data['registrations'] as $registration) : ?>
                                        <tr>
                                            <td><?php echo $registration->registration_number; ?></td>
                                            <td><?php echo $registration->year . ' ' . $registration->make . ' ' . $registration->model; ?></td>
                                            <td><?php echo $registration->owner_name; ?></td>
                                            <td><?php echo $registration->category_name; ?></td>
                                            <td>
                                                <?php if($registration->status == 'pending') : ?>
                                                    <span class="badge bg-warning text-dark">Pending</span>
                                                <?php elseif($registration->status == 'approved') : ?>
                                                    <span class="badge bg-success">Approved</span>
                                                <?php elseif($registration->status == 'rejected') : ?>
                                                    <span class="badge bg-danger">Rejected</span>
                                                <?php elseif($registration->status == 'cancelled') : ?>
                                                    <span class="badge bg-secondary">Cancelled</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if(isset($registration->payment_status)) : ?>
                                                    <?php if($registration->payment_status == 'pending') : ?>
                                                        <span class="badge bg-warning text-dark">Pending</span>
                                                    <?php elseif($registration->payment_status == 'completed') : ?>
                                                        <span class="badge bg-success">Paid</span>
                                                    <?php elseif($registration->payment_status == 'failed') : ?>
                                                        <span class="badge bg-danger">Failed</span>
                                                    <?php elseif($registration->payment_status == 'refunded') : ?>
                                                        <span class="badge bg-info">Refunded</span>
                                                    <?php endif; ?>
                                                <?php else : ?>
                                                    <span class="badge bg-secondary">N/A</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="<?php echo URLROOT; ?>/coordinator/view_registration/<?php echo $registration->id; ?>" class="btn btn-sm btn-primary">
                                                        <i class="fas fa-eye"></i> View
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-success"
                                                            onclick='openIndividualMessageModal(<?php echo intval($registration->user_id); ?>, <?php echo json_encode($registration->owner_name ?? 'Unknown User'); ?>)'
                                                            title="Send Message">
                                                        <i class="fas fa-envelope"></i> Message
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal<?php echo $registration->id; ?>">
                                                        <i class="fas fa-trash"></i> Delete
                                                    </button>
                                                </div>
                                                
                                                <!-- Delete Modal -->
                                                <div class="modal fade" id="deleteModal<?php echo $registration->id; ?>" tabindex="-1" aria-labelledby="deleteModalLabel<?php echo $registration->id; ?>" aria-hidden="true">
                                                    <div class="modal-dialog">
                                                        <div class="modal-content">
                                                            <div class="modal-header bg-danger text-white">
                                                                <h5 class="modal-title" id="deleteModalLabel<?php echo $registration->id; ?>">Confirm Delete</h5>
                                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                            </div>
                                                            <div class="modal-body">
                                                                <p>Are you sure you want to delete this registration?</p>
                                                                <p><strong>Registration #:</strong> <?php echo $registration->registration_number; ?></p>
                                                                <p><strong>Vehicle:</strong> <?php echo $registration->year . ' ' . $registration->make . ' ' . $registration->model; ?></p>
                                                                <p><strong>Owner:</strong> <?php echo $registration->owner_name; ?></p>
                                                                <p class="text-danger"><strong>Warning:</strong> This action cannot be undone.</p>
                                                            </div>
                                                            <div class="modal-footer">
                                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                                <form action="<?php echo URLROOT; ?>/coordinator/delete_registration/<?php echo $registration->id; ?>" method="post">
                                                                    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                                                                    <button type="submit" class="btn btn-danger">Delete Registration</button>
                                                                </form>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Message Modal -->
<div class="modal fade" id="bulkMessageModal" tabindex="-1" aria-labelledby="bulkMessageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bulkMessageModalLabel">
                    <i class="fas fa-envelope me-2"></i>Message All Registered Users
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="bulkMessageForm">
                    <div class="mb-3">
                        <label for="bulkSubject" class="form-label">Subject</label>
                        <input type="text" class="form-control" id="bulkSubject" required>
                    </div>
                    <div class="mb-3">
                        <label for="bulkMessage" class="form-label">Message</label>
                        <textarea class="form-control" id="bulkMessage" rows="5" required></textarea>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="bulkAllowReplies">
                            <label class="form-check-label" for="bulkAllowReplies">
                                <i class="fas fa-reply me-1"></i>Allow replies to this message
                            </label>
                            <small class="form-text text-muted d-block">
                                When checked, recipients will be able to reply to this message.
                            </small>
                        </div>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        This message will be sent to all <?php echo count($data['registrations']); ?> registered users for this show.
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="sendBulkMessage()">
                    <i class="fas fa-paper-plane me-1"></i>Send to All
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Individual Message Modal -->
<div class="modal fade" id="individualMessageModal" tabindex="-1" aria-labelledby="individualMessageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="individualMessageModalLabel">
                    <i class="fas fa-envelope me-2"></i>Send Message
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="individualMessageForm">
                    <input type="hidden" id="recipientUserId">
                    <div class="mb-3">
                        <label class="form-label">To:</label>
                        <div class="form-control-plaintext" id="recipientName"></div>
                    </div>
                    <div class="mb-3">
                        <label for="individualSubject" class="form-label">Subject</label>
                        <input type="text" class="form-control" id="individualSubject" required>
                    </div>
                    <div class="mb-3">
                        <label for="individualMessage" class="form-label">Message</label>
                        <textarea class="form-control" id="individualMessage" rows="5" required></textarea>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="individualAllowReplies">
                            <label class="form-check-label" for="individualAllowReplies">
                                <i class="fas fa-reply me-1"></i>Allow replies to this message
                            </label>
                            <small class="form-text text-muted d-block">
                                When checked, the recipient will be able to reply to this message.
                            </small>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="sendIndividualMessage()">
                    <i class="fas fa-paper-plane me-1"></i>Send Message
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Messaging functionality
function openBulkMessageModal() {
    const modal = new bootstrap.Modal(document.getElementById('bulkMessageModal'));
    modal.show();
}

function openIndividualMessageModal(userId, userName) {
    document.getElementById('recipientUserId').value = userId;
    document.getElementById('recipientName').textContent = userName;

    const modal = new bootstrap.Modal(document.getElementById('individualMessageModal'));
    modal.show();
}

function sendBulkMessage() {
    const subject = document.getElementById('bulkSubject').value;
    let message = document.getElementById('bulkMessage').value;
    const allowReplies = document.getElementById('bulkAllowReplies').checked;

    if (!subject || !message) {
        alert('Please fill in all fields');
        return;
    }

    // Add [reply] marker if replies are allowed
    if (allowReplies) {
        message = message + ' [reply]';
    }

    // Disable button to prevent double-click
    const sendButton = event.target;
    sendButton.disabled = true;
    sendButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Sending...';

    fetch('<?php echo URLROOT; ?>/coordinator/sendBulkMessage', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            show_id: <?php echo $data['show']->id; ?>,
            subject: subject,
            message: message,
            requires_reply: allowReplies
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Messages sent successfully to ' + data.count + ' users!');
            bootstrap.Modal.getInstance(document.getElementById('bulkMessageModal')).hide();
            document.getElementById('bulkMessageForm').reset();
        } else {
            alert('Error: ' + (data.error || 'Failed to send messages'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Network error occurred');
    })
    .finally(() => {
        sendButton.disabled = false;
        sendButton.innerHTML = '<i class="fas fa-paper-plane me-1"></i>Send to All';
    });
}

function sendIndividualMessage() {
    const userId = document.getElementById('recipientUserId').value;
    const subject = document.getElementById('individualSubject').value;
    let message = document.getElementById('individualMessage').value;
    const allowReplies = document.getElementById('individualAllowReplies').checked;

    if (!subject || !message) {
        alert('Please fill in all fields');
        return;
    }

    // Add [reply] marker if replies are allowed
    if (allowReplies) {
        message = message + ' [reply]';
    }

    // Disable button to prevent double-click
    const sendButton = event.target;
    sendButton.disabled = true;
    sendButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Sending...';

    fetch('<?php echo URLROOT; ?>/coordinator/sendIndividualMessage', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            to_user_id: userId,
            show_id: <?php echo $data['show']->id; ?>,
            subject: subject,
            message: message,
            requires_reply: allowReplies
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Message sent successfully!');
            bootstrap.Modal.getInstance(document.getElementById('individualMessageModal')).hide();
            document.getElementById('individualMessageForm').reset();
        } else {
            alert('Error: ' + (data.error || 'Failed to send message'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Network error occurred');
    })
    .finally(() => {
        sendButton.disabled = false;
        sendButton.innerHTML = '<i class="fas fa-paper-plane me-1"></i>Send Message';
    });
}
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>