<?php
/**
 * Enhanced Ticket Number Service
 * 
 * Handles generation of role-based ticket numbers:
 * - Admin: RER-A25-22-001 (A + 2-digit year + show_id + sequence)
 * - Coordinator: RER-C25-22-001 (C + 2-digit year + show_id + sequence)  
 * - Judge/Staff: RER-2025-001 (global tickets, no show association)
 */
class EnhancedTicketService {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Generate ticket number based on user role and context
     */
    public function generateTicketNumber($userRole = 'system', $showId = null, $userId = null) {
        // If userId provided but no role, determine role from user
        if ($userId && !$userRole) {
            $userRole = $this->getUserRole($userId);
        }
        
        $year = date('Y');
        $shortYear = date('y'); // 2-digit year
        
        switch ($userRole) {
            case 'admin':
                return $this->generateAdminTicket($shortYear, $showId);
                
            case 'coordinator':
                return $this->generateCoordinatorTicket($shortYear, $showId);
                
            case 'judge':
            case 'staff':
            case 'system':
            default:
                return $this->generateGlobalTicket($year);
        }
    }
    
    /**
     * Generate admin ticket: RER-A25-22-001
     */
    private function generateAdminTicket($shortYear, $showId) {
        if (!$showId) {
            // If no show ID, fall back to global ticket
            return $this->generateGlobalTicket(date('Y'));
        }
        
        $prefix = "RER-A{$shortYear}-{$showId}-";
        
        // Get the highest ticket number for this admin/show/year
        $this->db->query("SELECT ticket_number FROM messages 
                         WHERE ticket_number LIKE :prefix 
                         ORDER BY ticket_number DESC LIMIT 1");
        $this->db->bind(':prefix', $prefix . '%');
        $result = $this->db->single();
        
        if ($result) {
            $lastNumber = (int)substr($result->ticket_number, -3);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }
        
        return $prefix . str_pad($newNumber, 3, '0', STR_PAD_LEFT);
    }
    
    /**
     * Generate coordinator ticket: RER-C25-22-001
     */
    private function generateCoordinatorTicket($shortYear, $showId) {
        if (!$showId) {
            // Coordinators must have a show ID
            throw new Exception("Coordinator tickets require a show ID");
        }
        
        $prefix = "RER-C{$shortYear}-{$showId}-";
        
        // Get the highest ticket number for this coordinator/show/year
        $this->db->query("SELECT ticket_number FROM messages 
                         WHERE ticket_number LIKE :prefix 
                         ORDER BY ticket_number DESC LIMIT 1");
        $this->db->bind(':prefix', $prefix . '%');
        $result = $this->db->single();
        
        if ($result) {
            $lastNumber = (int)substr($result->ticket_number, -3);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }
        
        return $prefix . str_pad($newNumber, 3, '0', STR_PAD_LEFT);
    }
    
    /**
     * Generate global ticket: RER-2025-001 (for judges, staff, system)
     */
    private function generateGlobalTicket($year) {
        $prefix = "RER-{$year}-";
        
        // Get the highest ticket number for this year (excluding admin/coordinator tickets)
        $this->db->query("SELECT ticket_number FROM messages 
                         WHERE ticket_number LIKE :prefix 
                         AND ticket_number NOT LIKE '%A%' 
                         AND ticket_number NOT LIKE '%C%'
                         ORDER BY ticket_number DESC LIMIT 1");
        $this->db->bind(':prefix', $prefix . '%');
        $result = $this->db->single();
        
        if ($result) {
            $lastNumber = (int)substr($result->ticket_number, -3);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }
        
        return $prefix . str_pad($newNumber, 3, '0', STR_PAD_LEFT);
    }
    
    /**
     * Parse ticket number to extract information
     */
    public function parseTicketNumber($ticketNumber) {
        // Admin ticket: RER-A25-22-001
        if (preg_match('/^RER-A(\d{2})-(\d+)-(\d{3})$/', $ticketNumber, $matches)) {
            return [
                'type' => 'admin',
                'year' => 2000 + (int)$matches[1],
                'show_id' => (int)$matches[2],
                'sequence' => (int)$matches[3]
            ];
        }
        
        // Coordinator ticket: RER-C25-22-001
        if (preg_match('/^RER-C(\d{2})-(\d+)-(\d{3})$/', $ticketNumber, $matches)) {
            return [
                'type' => 'coordinator',
                'year' => 2000 + (int)$matches[1],
                'show_id' => (int)$matches[2],
                'sequence' => (int)$matches[3]
            ];
        }
        
        // Global ticket: RER-2025-001
        if (preg_match('/^RER-(\d{4})-(\d{3})$/', $ticketNumber, $matches)) {
            return [
                'type' => 'global',
                'year' => (int)$matches[1],
                'show_id' => null,
                'sequence' => (int)$matches[2]
            ];
        }
        
        return null; // Invalid ticket format
    }
    
    /**
     * Get user role from database
     */
    private function getUserRole($userId) {
        $this->db->query("SELECT role FROM users WHERE id = :user_id");
        $this->db->bind(':user_id', $userId);
        $result = $this->db->single();
        
        return $result ? $result->role : 'system';
    }
    
    /**
     * Find parent message by ticket number for threading
     */
    public function findParentMessageByTicket($ticketNumber) {
        $this->db->query("SELECT id FROM messages WHERE ticket_number = :ticket_number ORDER BY created_at ASC LIMIT 1");
        $this->db->bind(':ticket_number', $ticketNumber);
        $result = $this->db->single();
        
        return $result ? $result->id : null;
    }
    
    /**
     * Get show ID from ticket number
     */
    public function getShowIdFromTicket($ticketNumber) {
        $parsed = $this->parseTicketNumber($ticketNumber);
        return $parsed ? $parsed['show_id'] : null;
    }
    
    /**
     * Check if user has access to ticket based on role and show
     */
    public function userHasAccessToTicket($userId, $ticketNumber) {
        $userRole = $this->getUserRole($userId);
        $parsed = $this->parseTicketNumber($ticketNumber);
        
        if (!$parsed) {
            return false;
        }
        
        // Admins have access to all tickets
        if ($userRole === 'admin') {
            return true;
        }
        
        // Coordinators only have access to their show tickets
        if ($userRole === 'coordinator' && $parsed['type'] === 'coordinator') {
            // Check if user is coordinator for this show
            $this->db->query("SELECT COUNT(*) as count FROM show_roles 
                             WHERE user_id = :user_id AND show_id = :show_id AND role = 'coordinator'");
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':show_id', $parsed['show_id']);
            $result = $this->db->single();
            
            return $result && $result->count > 0;
        }
        
        // Other roles don't have access to email tickets
        return false;
    }
    
    /**
     * Get ticket statistics
     */
    public function getTicketStats($year = null) {
        if (!$year) {
            $year = date('Y');
        }
        
        $shortYear = substr($year, -2);
        
        // Count admin tickets
        $this->db->query("SELECT COUNT(*) as count FROM messages WHERE ticket_number LIKE 'RER-A{$shortYear}-%'");
        $adminCount = $this->db->single()->count;
        
        // Count coordinator tickets
        $this->db->query("SELECT COUNT(*) as count FROM messages WHERE ticket_number LIKE 'RER-C{$shortYear}-%'");
        $coordinatorCount = $this->db->single()->count;
        
        // Count global tickets
        $this->db->query("SELECT COUNT(*) as count FROM messages WHERE ticket_number LIKE 'RER-{$year}-%' AND ticket_number NOT LIKE '%A%' AND ticket_number NOT LIKE '%C%'");
        $globalCount = $this->db->single()->count;
        
        return [
            'year' => $year,
            'admin_tickets' => $adminCount,
            'coordinator_tickets' => $coordinatorCount,
            'global_tickets' => $globalCount,
            'total_tickets' => $adminCount + $coordinatorCount + $globalCount
        ];
    }
}
